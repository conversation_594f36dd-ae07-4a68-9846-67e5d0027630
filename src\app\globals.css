@import "tailwindcss";

/* ===== DESIGN SYSTEM VARIABLES ===== */
:root {
  /* Colors */
  --background: #ffffff;
  --foreground: #1a1a1a;

  /* Brand Colors */
  --primary: #006c95;
  --primary-light: #0088b8;
  --primary-dark: #004d6b;
  --secondary: #f59e0b;
  --secondary-light: #fbbf24;
  --secondary-dark: #d97706;

  /* Accent Colors */
  --accent-blue: #0ea5e9;
  --accent-blue-light: #38bdf8;
  --accent-blue-dark: #0284c7;

  /* Neutral Colors */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

@theme {
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent-blue);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #1a1a1a;
  }
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-poppins), "Segoe UI", Tahoma, Geneva, Verdana,
    sans-serif;
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== COMPONENT UTILITIES ===== */

/* Button Styles */
.btn {
  @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-[var(--primary)] text-white hover:bg-[var(--primary-dark)] focus:ring-[var(--primary)] shadow-md hover:shadow-lg transform hover:scale-105;
}

.btn-secondary {
  @apply bg-[var(--secondary)] text-gray-900 hover:bg-[var(--secondary-dark)] focus:ring-[var(--secondary)] shadow-md hover:shadow-lg transform hover:scale-105;
}

.btn-outline {
  @apply border-2 border-[var(--primary)] text-[var(--primary)] hover:bg-[var(--primary)] hover:text-white focus:ring-[var(--primary)];
}

.btn-ghost {
  @apply text-[var(--primary)] hover:bg-[var(--gray-100)] focus:ring-[var(--primary)];
}

.btn-sm {
  @apply px-4 py-2 text-xs;
}

.btn-lg {
  @apply px-8 py-4 text-base;
}

/* Input Styles */
.input {
  @apply w-full px-4 py-3 border border-[var(--gray-300)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)] outline-none transition-colors duration-200 font-poppins;
}

.input-error {
  @apply border-[var(--error)] focus:ring-[var(--error)] focus:border-[var(--error)];
}

/* Card Styles */
.card {
  @apply bg-white rounded-xl shadow-[var(--shadow-md)] border border-[var(--gray-100)] overflow-hidden transition-all duration-300;
}

.card-hover {
  @apply hover:shadow-[var(--shadow-lg)] hover:scale-[1.02];
}

.card-interactive {
  @apply cursor-pointer hover:shadow-[var(--shadow-xl)] hover:scale-[1.03] transform transition-all duration-300;
}

/* Text Styles */
.text-heading {
  @apply font-bold text-[var(--foreground)] font-poppins;
}

.text-subheading {
  @apply font-semibold text-[var(--gray-700)] font-poppins;
}

.text-body {
  @apply text-[var(--gray-600)] font-poppins leading-relaxed;
}

.text-caption {
  @apply text-sm text-[var(--gray-500)] font-poppins;
}

/* Badge Styles */
.badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-[var(--primary)] text-white;
}

.badge-secondary {
  @apply bg-[var(--secondary)] text-gray-900;
}

.badge-success {
  @apply bg-[var(--success)] text-white;
}

.badge-warning {
  @apply bg-[var(--warning)] text-gray-900;
}

.badge-error {
  @apply bg-[var(--error)] text-white;
}

.badge-outline {
  @apply border border-[var(--gray-300)] text-[var(--gray-700)] bg-transparent;
}

/* Loading States */
.loading {
  @apply animate-pulse;
}

.skeleton {
  @apply bg-[var(--gray-200)] rounded animate-pulse;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}

/* Focus Styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2;
}

/* Container Styles */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-12 lg:py-16;
}

/* Travel-specific Utilities */
.hero-gradient {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--accent-blue) 100%
  );
}

.travel-card-shadow {
  box-shadow: 0 10px 30px rgba(0, 108, 149, 0.1);
}

.travel-card-shadow:hover {
  box-shadow: 0 20px 40px rgba(0, 108, 149, 0.15);
}
