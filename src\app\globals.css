@import "tailwindcss";

@theme {
  /* Brand Colors */
  --color-primary: #006c95;
  --color-primary-light: #0088b8;
  --color-primary-dark: #004d6b;
  --color-secondary: #f59e0b;
  --color-secondary-light: #fbbf24;
  --color-secondary-dark: #d97706;

  /* Accent Colors */
  --color-accent: #0ea5e9;
  --color-accent-light: #38bdf8;
  --color-accent-dark: #0284c7;

  /* Status Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Custom Shadows */
  --shadow-travel: 0 10px 30px rgb(0 108 149 / 0.1);
  --shadow-travel-hover: 0 20px 40px rgb(0 108 149 / 0.15);

  /* Custom Spacing */
  --spacing-section: 3rem;
  --spacing-section-lg: 4rem;
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: white;
  color: #1a1a1a;
  font-family: var(--font-poppins), "Segoe UI", Tahoma, Geneva, Verdana,
    sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== COMPONENT UTILITIES ===== */

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
  text-decoration: none;
  font-family: var(--font-poppins), sans-serif;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 108, 149, 0.5);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #006c95;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background-color: #004d6b;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-secondary {
  background-color: #f59e0b;
  color: #1a1a1a;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover:not(:disabled) {
  background-color: #d97706;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-outline {
  border: 2px solid #006c95;
  color: #006c95;
  background-color: transparent;
}

.btn-outline:hover:not(:disabled) {
  background-color: #006c95;
  color: white;
}

.btn-ghost {
  color: #006c95;
  background-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: #f1f5f9;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Input Styles */
.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #cbd5e1;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.2s ease-in-out;
  font-family: var(--font-poppins), sans-serif;
  font-size: 0.875rem;
}

.input:focus {
  border-color: #006c95;
  box-shadow: 0 0 0 2px rgba(0, 108, 149, 0.2);
}

.input-error {
  border-color: #ef4444;
}

.input-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.card-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1);
  transform: scale(1.02);
}

.card-interactive {
  cursor: pointer;
  transform: translateZ(0);
  transition: all 0.3s ease-in-out;
}

.card-interactive:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
  transform: scale(1.03);
}

/* Text Styles */
.text-heading {
  font-weight: 700;
  color: #1a1a1a;
  font-family: var(--font-poppins), sans-serif;
}

.text-subheading {
  font-weight: 600;
  color: #334155;
  font-family: var(--font-poppins), sans-serif;
}

.text-body {
  color: #475569;
  font-family: var(--font-poppins), sans-serif;
  line-height: 1.625;
}

.text-caption {
  font-size: 0.875rem;
  color: #64748b;
  font-family: var(--font-poppins), sans-serif;
}

/* Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  font-family: var(--font-poppins), sans-serif;
}

.badge-primary {
  background-color: #006c95;
  color: white;
}

.badge-secondary {
  background-color: #f59e0b;
  color: #1a1a1a;
}

.badge-success {
  background-color: #10b981;
  color: white;
}

.badge-warning {
  background-color: #f59e0b;
  color: #1a1a1a;
}

.badge-error {
  background-color: #ef4444;
  color: white;
}

.badge-outline {
  border: 1px solid #cbd5e1;
  color: #334155;
  background-color: transparent;
}

/* Loading States */
.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton {
  background-color: #e2e8f0;
  border-radius: 0.25rem;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}

/* Focus Styles */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 108, 149, 0.5);
}

/* Container Styles */
.container-custom {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.section-padding {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

/* Travel-specific Utilities */
.hero-gradient {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--accent-blue) 100%
  );
}

.travel-card-shadow {
  box-shadow: 0 10px 30px rgba(0, 108, 149, 0.1);
}

.travel-card-shadow:hover {
  box-shadow: 0 20px 40px rgba(0, 108, 149, 0.15);
}
