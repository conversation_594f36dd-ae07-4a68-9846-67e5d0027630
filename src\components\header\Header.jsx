"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Menu, X, Phone, Mail } from "lucide-react";
import Button from "@/components/ui/Button";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();

  // Handle scroll effect for background
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { label: "Accueil", href: "/" },
    { label: "Destinations", href: "/destinations" },
    { label: "Blog", href: "/blog" },
    { label: "Actualités", href: "/actualites" },
    { label: "Contact", href: "/contact" },
  ];

  const isActiveLink = (href) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  return (
    <>
      {/* Top Contact Bar - Hidden on mobile */}
      <div className="hidden lg:block bg-[var(--primary)] text-white py-2 relative z-50">
        <div className="container-custom">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 transition-colors hover:text-[var(--secondary-light)]">
                <Phone className="h-4 w-4" />
                <span>+213 123 456 789</span>
              </div>
              <div className="flex items-center space-x-2 transition-colors hover:text-[var(--secondary-light)]">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className="text-sm font-medium">
              Votre partenaire de confiance pour des voyages inoubliables
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header
        className={`fixed left-0 w-full z-40 transition-all duration-300 ${
          isScrolled
            ? "top-0 bg-white/95 backdrop-blur-md shadow-lg"
            : "top-0 lg:top-10 bg-transparent"
        }`}
        role="banner"
      >
        <div className="container-custom">
          <div className="flex items-center justify-between py-4 lg:py-6">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link
                href="/"
                className="block transition-transform duration-200 hover:scale-105"
                aria-label="Bouguerrouche Travel - Accueil"
              >
                <img
                  src="/images/logo.svg"
                  alt="Bouguerrouche Travel"
                  className="h-10 lg:h-12 w-auto"
                />
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav
              className="hidden lg:flex items-center space-x-8"
              role="navigation"
              aria-label="Navigation principale"
            >
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`relative text-base font-medium font-poppins transition-all duration-200 group ${
                    isActiveLink(item.href)
                      ? "text-[var(--primary)]"
                      : isScrolled
                      ? "text-[var(--gray-700)] hover:text-[var(--primary)]"
                      : "text-[var(--gray-600)] hover:text-[var(--secondary)]"
                  }`}
                >
                  {item.label}
                  {/* Active indicator */}
                  <span
                    className={`absolute -bottom-1 left-0 w-full h-0.5 bg-[var(--primary)] transform transition-transform duration-200 ${
                      isActiveLink(item.href)
                        ? "scale-x-100"
                        : "scale-x-0 group-hover:scale-x-100"
                    }`}
                  />
                </Link>
              ))}
            </nav>

            {/* CTA Button - Desktop */}
            <div className="hidden lg:flex items-center space-x-4">
              <Link href="/contact">
                <Button
                  variant="secondary"
                  size="md"
                  className="rounded-full font-poppins"
                >
                  Réserver maintenant
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden">
              <button
                onClick={toggleMenu}
                className={`inline-flex items-center justify-center p-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[var(--primary)] ${
                  isScrolled
                    ? "text-[var(--gray-700)] hover:text-[var(--primary)] hover:bg-[var(--gray-100)]"
                    : "text-white hover:text-[var(--secondary)] hover:bg-white/10"
                }`}
                aria-expanded={isMenuOpen}
                aria-controls="mobile-menu"
                aria-label={isMenuOpen ? "Fermer le menu" : "Ouvrir le menu"}
              >
                {isMenuOpen ? (
                  <X className="block h-6 w-6" aria-hidden="true" />
                ) : (
                  <Menu className="block h-6 w-6" aria-hidden="true" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          id="mobile-menu"
          className={`lg:hidden transition-all duration-300 ease-in-out ${
            isMenuOpen
              ? "max-h-screen opacity-100"
              : "max-h-0 opacity-0 overflow-hidden"
          }`}
          role="navigation"
          aria-label="Navigation mobile"
        >
          <div className="bg-white/95 backdrop-blur-md shadow-lg border-t border-gray-200">
            <div className="px-4 py-6 space-y-4">
              {navItems.map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block px-4 py-3 rounded-lg text-base font-medium font-poppins transition-all duration-200 transform ${
                    isActiveLink(item.href)
                      ? "text-[var(--primary)] bg-[var(--accent-blue)]/10 border-l-4 border-[var(--primary)]"
                      : "text-[var(--gray-700)] hover:text-[var(--primary)] hover:bg-[var(--gray-50)]"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                  style={{
                    animationDelay: `${index * 50}ms`,
                    animation: isMenuOpen
                      ? "slideInFromRight 0.3s ease-out forwards"
                      : "none",
                  }}
                >
                  {item.label}
                </Link>
              ))}

              {/* Mobile CTA */}
              <div className="pt-4 border-t border-[var(--gray-200)]">
                <Link href="/contact" onClick={() => setIsMenuOpen(false)}>
                  <Button
                    variant="secondary"
                    size="lg"
                    className="w-full font-poppins"
                  >
                    Réserver maintenant
                  </Button>
                </Link>
              </div>

              {/* Mobile Contact Info */}
              <div className="pt-4 border-t border-[var(--gray-200)] space-y-3">
                <div className="flex items-center space-x-3 text-[var(--gray-600)]">
                  <Phone className="h-4 w-4 text-[var(--primary)]" />
                  <span className="text-sm">+213 123 456 789</span>
                </div>
                <div className="flex items-center space-x-3 text-[var(--gray-600)]">
                  <Mail className="h-4 w-4 text-[var(--primary)]" />
                  <span className="text-sm"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes slideInFromRight {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `}</style>
    </>
  );
}
