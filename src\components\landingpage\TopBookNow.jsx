"use client";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";

const hotels = [
  {
    name: "Joya Paradise",
    location: "Djerba - Tunisie",
    stars: 4,
    offer: "Offre exclusive - bon emplacement - 1er enf -6 ans gratuit - luxe",
    price: 13500,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: [],
  },
  {
    name: "Hôtel Kanta",
    location: "Sousse - Tunisie",
    stars: 4,
    offer: "1er Enfant -6 ans Gratuit",
    price: 11700,
    currency: "DA",
    image: "/images/back.png",
    discount: "-18%",
    highlights: [],
  },
  {
    name: "Travelodo Village Africa Jade Thalasso",
    location: "Korba - Tunisie",
    stars: 4,
    offer:
      "1er enfant -12ans gratuit / réduction -50% sur 2 nuit avec samedi // réduction -50% senior +60ans sauf les samedis / Early Booking 20% pour toute réservation effectuée avant le 31/05 pour les séjours",
    price: 28000,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: [],
  },
  {
    name: "Radisson Blu Resort & Thalasso Hammamet",
    location: "Hammamet - Tunisie",
    stars: 5,
    offer: "ACTION EARLY BOOKING REDUCTION 15%",
    price: 19500,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: ["green"],
  },
  {
    name: "Yadis Hammamet",
    location: "Hammamet - Tunisie",
    stars: 4,
    offer:
      "Hôtel en Promo exclusive - 1er enfant -6ans Gratuit sauf la période du 04/07 au 13/09 - N'accepte pas les jeunes célibataires",
    price: 12600,
    currency: "DA",
    image: "/images/back.png",
    discount: "-29%",
    highlights: ["green"],
  },
  {
    name: "Riviera",
    location: "Sousse - Tunisie",
    stars: 4,
    offer:
      "N'accepte pas les jeunes célibataires / Promo Enfant -6 ans gratuit",
    price: 16800,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: ["green"],
  },
];

export default function TopBookNow() {
  return (
    <section className="w-full section-padding bg-white">
      <div className="container-custom">
        <h2 className="text-heading text-3xl md:text-4xl text-center mb-10 tracking-tight text-primary">
          TOP BOOK NOW
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {hotels.map((hotel, idx) => (
            <Card
              key={idx}
              className="travel-card-shadow flex flex-col relative"
              hover={true}
              padding="none"
            >
              <div className="relative w-full h-44">
                <Image
                  src={hotel.image}
                  alt={hotel.name}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
                {hotel.discount && (
                  <div className="absolute top-3 right-3">
                    <Badge variant="error" className="shadow-md">
                      {hotel.discount}
                    </Badge>
                  </div>
                )}
              </div>
              <CardContent className="flex-1 flex flex-col p-4">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-heading text-lg">{hotel.name}</span>
                  <span className="text-secondary text-sm">
                    {"★".repeat(hotel.stars)}
                  </span>
                </div>
                <div className="text-caption mb-2">{hotel.location}</div>
                <div className="text-body text-xs mb-3 flex flex-col gap-1">
                  {hotel.offer && (
                    <span
                      className={
                        hotel.highlights.includes("green")
                          ? "text-success font-semibold"
                          : ""
                      }
                    >
                      {hotel.offer}
                    </span>
                  )}
                </div>
                <div className="flex items-end justify-between mt-auto pt-2">
                  <div className="text-base font-bold text-primary">
                    à partir de{" "}
                    <span className="text-xl text-gray-900">
                      {hotel.price.toLocaleString()} {hotel.currency}
                    </span>
                  </div>
                  <Button variant="primary" size="sm">
                    VOIR L'OFFRE
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
