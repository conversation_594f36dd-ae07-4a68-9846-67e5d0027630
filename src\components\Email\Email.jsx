"use client";
import Image from "next/image";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Card } from "@/components/ui/Card";
import { useState } from "react";

export default function Email() {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setEmail("");
      // You could add a success message here
    }, 1000);
  };

  return (
    <div className="flex justify-center items-center section-padding bg-white">
      <Card
        className="flex flex-col md:flex-row items-center bg-[var(--gray-100)] rounded-3xl overflow-hidden max-w-6xl w-full"
        padding="none"
      >
        {/* Left Image */}
        <div className="w-full md:w-1/2 relative aspect-video md:aspect-auto md:h-96">
          <Image
            src="/images/email.jpg"
            alt="Luxury Resort"
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Right Content */}
        <div className="w-full md:w-1/2 p-8 text-center md:text-left">
          <h2 className="text-heading text-2xl md:text-3xl mb-6">
            Bénéficiez d'offres spéciales et plus, <br />
            encore de{" "}
            <span className="text-[var(--primary)]">BOUGUERROUCHE</span>
          </h2>

          {/* Email input with button inside */}
          <form onSubmit={handleSubmit} className="relative max-w-xl mx-auto">
            <div className="flex gap-2">
              <Input
                type="email"
                placeholder="Entrez votre email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="flex-1 rounded-full"
              />
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                className="rounded-full px-8"
              >
                S'abonner
              </Button>
            </div>
          </form>
        </div>
      </Card>
    </div>
  );
}
